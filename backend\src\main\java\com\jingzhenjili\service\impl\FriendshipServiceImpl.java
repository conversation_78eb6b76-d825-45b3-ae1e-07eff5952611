package com.jingzhenjili.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingzhenjili.common.ResultCode;
import com.jingzhenjili.dto.FriendRequestDTO;
import com.jingzhenjili.entity.Friendship;
import com.jingzhenjili.entity.User;
import com.jingzhenjili.exception.BusinessException;
import com.jingzhenjili.mapper.FriendshipMapper;
import com.jingzhenjili.service.FriendshipService;
import com.jingzhenjili.service.UserService;
import com.jingzhenjili.vo.FriendVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.StringUtils;

/**
 * 好友关系服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
public class FriendshipServiceImpl extends ServiceImpl<FriendshipMapper, Friendship> implements FriendshipService {

    @Autowired
    private FriendshipMapper friendshipMapper;

    @Autowired
    private UserService userService;

    @Override
    @Transactional
    public Boolean sendFriendRequest(Long userId, FriendRequestDTO friendRequestDTO) {
        Long friendId = friendRequestDTO.getFriendId();

        // 检查是否添加自己
        if (userId.equals(friendId)) {
            throw new BusinessException(ResultCode.CANNOT_ADD_SELF);
        }

        // 检查目标用户是否存在
        User friend = userService.getById(friendId);
        if (friend == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查是否已经是好友
        if (isFriend(userId, friendId)) {
            throw new BusinessException(ResultCode.FRIEND_ALREADY_EXISTS);
        }

        // 检查是否已经发送过请求
        Friendship existingRequest = friendshipMapper.checkFriendship(userId, friendId);
        if (existingRequest != null) {
            if (existingRequest.getStatus() == 0) {
                throw new BusinessException(ResultCode.FRIEND_REQUEST_EXISTS);
            } else if (existingRequest.getStatus() == 2) {
                // 如果之前被拒绝，可以重新发送
                existingRequest.setStatus(0);
                existingRequest.setRemark(friendRequestDTO.getRemark());
                return updateById(existingRequest);
            }
        }

        // 若存在被软删除的历史记录，进行“复活”而非插入，避免唯一键冲突
        Friendship anyRecord = friendshipMapper.findAnyFriendship(userId, friendId);
        if (anyRecord != null && (anyRecord.getDeleted() != null && anyRecord.getDeleted() == 1)) {
            anyRecord.setDeleted(0);
            anyRecord.setStatus(0);
            anyRecord.setRemark(friendRequestDTO.getRemark());
            boolean revived = updateById(anyRecord);
            if (revived) {
                log.info("复活软删除好友请求: {} -> {}", userId, friendId);
            }
            return revived;
        }

        // 创建好友请求
        Friendship friendship = new Friendship();
        friendship.setUserId(userId);
        friendship.setFriendId(friendId);
        friendship.setStatus(0); // 待确认
        friendship.setRemark(friendRequestDTO.getRemark());

        boolean result = save(friendship);
        if (result) {
            log.info("用户{}向用户{}发送好友请求", userId, friendId);
        }

        return result;
    }

    @Override
    @Transactional
    public Boolean acceptFriendRequest(Long userId, Long friendshipId) {
        // 获取好友请求
        Friendship friendship = getById(friendshipId);
        if (friendship == null) {
            throw new BusinessException(ResultCode.FRIEND_REQUEST_NOT_FOUND);
        }

        // 检查是否为目标用户
        if (!friendship.getFriendId().equals(userId)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        // 检查请求状态
        if (friendship.getStatus() != 0) {
            throw new BusinessException(ResultCode.FRIEND_REQUEST_NOT_FOUND, "好友请求已处理");
        }

        // 更新请求状态为已确认
        friendship.setStatus(1);
        boolean result1 = updateById(friendship);

        // 创建反向好友关系
        // 如果反向记录存在（含deleted=1），优先复活，否则新建
        boolean result2;
        Friendship reverseAny = friendshipMapper.findAnyFriendship(userId, friendship.getUserId());
        if (reverseAny != null) {
            reverseAny.setDeleted(0);
            reverseAny.setStatus(1);
            if (reverseAny.getRemark() == null) {
                reverseAny.setRemark("");
            }
            result2 = updateById(reverseAny);
        } else {
            Friendship reverseFriendship = new Friendship();
            reverseFriendship.setUserId(userId);
            reverseFriendship.setFriendId(friendship.getUserId());
            reverseFriendship.setStatus(1); // 已确认
            reverseFriendship.setRemark(""); // 默认空备注
            result2 = save(reverseFriendship);
        }

        boolean result = result1 && result2;
        if (result) {
            log.info("用户{}接受了用户{}的好友请求", userId, friendship.getUserId());
        }

        return result;
    }

    @Override
    @Transactional
    public Boolean rejectFriendRequest(Long userId, Long friendshipId) {
        // 获取好友请求
        Friendship friendship = getById(friendshipId);
        if (friendship == null) {
            throw new BusinessException(ResultCode.FRIEND_REQUEST_NOT_FOUND);
        }

        // 检查是否为目标用户
        if (!friendship.getFriendId().equals(userId)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        // 检查请求状态
        if (friendship.getStatus() != 0) {
            throw new BusinessException(ResultCode.FRIEND_REQUEST_NOT_FOUND, "好友请求已处理");
        }

        // 更新请求状态为已拒绝
        friendship.setStatus(2);
        boolean result = updateById(friendship);

        if (result) {
            log.info("用户{}拒绝了用户{}的好友请求", userId, friendship.getUserId());
        }

        return result;
    }

    @Override
    @Transactional
    public Boolean deleteFriend(Long userId, Long friendId) {
        // 检查是否为好友关系
        if (!isFriend(userId, friendId)) {
            throw new BusinessException(ResultCode.FRIEND_NOT_FOUND);
        }

        // 删除双向好友关系
        QueryWrapper<Friendship> wrapper1 = new QueryWrapper<>();
        wrapper1.eq("user_id", userId).eq("friend_id", friendId).eq("status", 1);
        
        QueryWrapper<Friendship> wrapper2 = new QueryWrapper<>();
        wrapper2.eq("user_id", friendId).eq("friend_id", userId).eq("status", 1);

        boolean result1 = remove(wrapper1);
        boolean result2 = remove(wrapper2);

        boolean result = result1 && result2;
        if (result) {
            log.info("用户{}删除了好友{}", userId, friendId);
        }

        return result;
    }

    @Override
    public List<FriendVO> getFriendList(Long userId) {
        List<FriendVO> friends = friendshipMapper.getFriendList(userId);
        // 设置描述信息
        friends.forEach(friend -> {
            friend.setGender(friend.getGender());
            friend.setStatus(friend.getStatus());
        });
        return friends;
    }

    @Override
    public List<FriendVO> getFriendRequests(Long userId) {
        List<FriendVO> requests = friendshipMapper.getFriendRequests(userId);
        // 设置描述信息
        requests.forEach(request -> {
            request.setGender(request.getGender());
            request.setStatus(request.getStatus());
        });
        return requests;
    }

    @Override
    public List<FriendVO> getSentFriendRequests(Long userId) {
        List<FriendVO> requests = friendshipMapper.getSentFriendRequests(userId);
        // 设置描述信息
        requests.forEach(request -> {
            request.setGender(request.getGender());
            request.setStatus(request.getStatus());
            request.setRequestStatus(request.getRequestStatus());
        });
        return requests;
    }

    @Override
    public Boolean isFriend(Long userId, Long friendId) {
        return friendshipMapper.isFriend(userId, friendId);
    }

    @Override
    public Integer getFriendCount(Long userId) {
        return friendshipMapper.getFriendCount(userId);
    }

    @Override
    public List<FriendVO> getCommonFriends(Long userId1, Long userId2) {
        List<FriendVO> commonFriends = friendshipMapper.getCommonFriends(userId1, userId2);
        // 设置描述信息
        commonFriends.forEach(friend -> {
            friend.setGender(friend.getGender());
        });
        return commonFriends;
    }

    @Override
    @Transactional
    public Boolean updateFriendRemark(Long userId, Long friendId, String remark) {
        QueryWrapper<Friendship> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).eq("friend_id", friendId).eq("status", 1);

        Friendship friendship = getOne(wrapper);
        if (friendship == null) {
            throw new BusinessException(ResultCode.FRIEND_NOT_FOUND);
        }

        friendship.setRemark(remark);
        boolean result = updateById(friendship);

        if (result) {
            log.info("用户{}更新了好友{}的备注", userId, friendId);
        }

        return result;
    }

    @Override
    public Integer checkFriendRequestStatus(Long userId, Long friendId) {
        Friendship friendship = friendshipMapper.checkFriendship(userId, friendId);
        return friendship != null ? friendship.getStatus() : null;
    }

    // ==================== 管理员相关方法实现 ====================

    @Override
    public IPage<Map<String, Object>> getAdminFriendshipPage(Integer page, Integer size, String username, Integer status) {
        Page<Friendship> pageParam = new Page<>(page, size);

        // 构建查询条件
        QueryWrapper<Friendship> queryWrapper = new QueryWrapper<>();

        // 状态筛选
        if (status != null) {
            queryWrapper.eq("status", status);
        }

        // 用户名搜索（这里简化处理，实际应该关联用户表查询）
        if (StringUtils.hasText(username)) {
            // TODO: 实现用户名搜索，需要关联用户表
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc("created_time");

        // 查询好友关系
        IPage<Friendship> friendshipPage = page(pageParam, queryWrapper);

        // 转换为Map格式（模拟数据，实际应该关联查询用户信息）
        IPage<Map<String, Object>> resultPage = new Page<>();
        resultPage.setCurrent(friendshipPage.getCurrent());
        resultPage.setSize(friendshipPage.getSize());
        resultPage.setTotal(friendshipPage.getTotal());

        List<Map<String, Object>> records = new ArrayList<>();
        for (Friendship friendship : friendshipPage.getRecords()) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", friendship.getId());
            record.put("userId", friendship.getUserId());
            record.put("friendId", friendship.getFriendId());
            record.put("status", friendship.getStatus());
            record.put("createdTime", friendship.getCreatedTime());
            record.put("updatedTime", friendship.getUpdatedTime());

            // TODO: 这里应该查询用户信息，设置用户名、头像等
            record.put("username", "用户" + friendship.getUserId());
            record.put("friendName", "用户" + friendship.getFriendId());
            record.put("userAvatar", "");
            record.put("friendAvatar", "");

            records.add(record);
        }
        resultPage.setRecords(records);

        return resultPage;
    }

    @Override
    @Transactional
    public Boolean adminDeleteFriendship(Long friendshipId) {
        Friendship friendship = getById(friendshipId);
        if (friendship == null) {
            throw new BusinessException(ResultCode.ERROR, "好友关系不存在");
        }

        // 物理删除好友关系
        boolean result = removeById(friendshipId);

        if (result) {
            log.info("管理员删除好友关系: {}", friendshipId);
        }

        return result;
    }
}
